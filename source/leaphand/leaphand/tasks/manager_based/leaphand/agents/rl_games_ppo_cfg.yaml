# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

# RL-Games configuration for LeapHand连续旋转任务
# 基于官方manipulation任务配置，针对连续旋转任务优化

params:
  seed: 42

  # environment wrapper clipping
  env:
    clip_observations: 5.0
    clip_actions: 1.0

  algo:
    name: a2c_continuous

  model:
    name: continuous_a2c_logstd

  network:
    name: actor_critic
    separate: True  # 支持非对称Actor-Critic
    
    space:
      continuous:
        mu_activation: None
        sigma_activation: None
        mu_init:
          name: default
        sigma_init:
          name: const_initializer
          val: 0
        fixed_sigma: True
    
    mlp:
      units: [512, 512, 256]
      activation: elu
      d2rl: False
      
      initializer:
        name: default
      regularizer:
        name: None

  load_checkpoint: False # flag which sets whether to load the checkpoint
  load_path: '' # path to the checkpoint to load

  config:
    name: leaphand_continuous_rot
    env_name: rlgpu
    device: 'cuda:0'
    device_name: 'cuda:0'
    multi_gpu: False
    ppo: True
    mixed_precision: False
    normalize_input: True
    normalize_value: True
    value_bootstrap: True
    num_actors: -1  # configured from the script (based on num_envs)
    reward_shaper:
      scale_value: 1.0
    normalize_advantage: True
    gamma: 0.99
    tau: 0.95
    learning_rate: 1e-4
    lr_schedule: adaptive
    schedule_type: legacy
    kl_threshold: 0.01
    score_to_win: 100000000
    max_epochs: 2000
    save_best_after: 200
    save_frequency: 100
    print_stats: True
    grad_norm: 1.0
    entropy_coef: 0.005  # 稍微增加熵系数以鼓励探索
    truncate_grads: True
    e_clip: 0.2
    horizon_length: 24  # 与RSL-RL保持一致
    minibatch_size: 100
    mini_epochs: 8
    critic_coef: 2
    clip_value: True
    clip_actions: False
    bounds_loss_coef: 0.0001
